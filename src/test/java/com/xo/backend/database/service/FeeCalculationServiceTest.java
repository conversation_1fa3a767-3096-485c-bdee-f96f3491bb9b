package com.xo.backend.database.service;

import com.xo.backend.database.entity.fees.FeeConfigEntity;
import com.xo.backend.database.repository.fees.FeeConfigRepository;
import com.xo.backend.error.exceptions.GeneralException;
import com.xo.backend.model.dto.fees.FeeCalculationRequest;
import com.xo.backend.model.dto.fees.FeeCalculationResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class FeeCalculationServiceTest {

    @Mock
    private FeeConfigRepository feeConfigRepository;

    @InjectMocks
    private FeeCalculationService feeCalculationService;

    private FeeConfigEntity feeConfig;
    private static final String VENUE_ID = "test-venue-123";

    @BeforeEach
    void setUp() {
        feeConfig = FeeConfigEntity.builder()
                .venueId(VENUE_ID)
                .appFeePerc(new BigDecimal("0.0500")) // 5%
                .appFeeFixed(new BigDecimal("2.00"))   // $2 fixed
                .appFeeMin(new BigDecimal("3.00"))     // $3 minimum
                .appFeeMax(new BigDecimal("50.00"))    // $50 maximum
                .pspFeePerc(new BigDecimal("0.0290"))  // 2.9%
                .pspFeeFixed(new BigDecimal("0.30"))   // $0.30 fixed
                .passPspFees(false)                    // Platform absorbs PSP fees
                .build();
    }

    @Test
    void shouldCalculateFeesCorrectly_WhenPassPspFeesIsFalse() {
        // Given
        BigDecimal netBookingPrice = new BigDecimal("100.00");
        FeeCalculationRequest request = FeeCalculationRequest.builder()
                .venueId(VENUE_ID)
                .netBookingPrice(netBookingPrice)
                .build();

        when(feeConfigRepository.findByVenueId(VENUE_ID)).thenReturn(Optional.of(feeConfig));

        // When
        FeeCalculationResult result = feeCalculationService.calculateFees(request);

        // Then
        assertThat(result.netBookingPrice()).isEqualTo(new BigDecimal("100.00"));
        assertThat(result.totalAppFee()).isEqualTo(new BigDecimal("7.00")); // 5% of 100 + 2 = 7 (above minimum)
        assertThat(result.passPspFeesToCustomer()).isFalse();
        assertThat(result.totalTransactionAmount()).isGreaterThan(netBookingPrice);
        assertThat(result.totalEstimatedPspFee()).isGreaterThan(BigDecimal.ZERO);
    }

    @Test
    void shouldCalculateFeesCorrectly_WhenPassPspFeesIsTrue() {
        // Given
        feeConfig.setPassPspFees(true);
        BigDecimal netBookingPrice = new BigDecimal("100.00");
        FeeCalculationRequest request = FeeCalculationRequest.builder()
                .venueId(VENUE_ID)
                .netBookingPrice(netBookingPrice)
                .build();

        when(feeConfigRepository.findByVenueId(VENUE_ID)).thenReturn(Optional.of(feeConfig));

        // When
        FeeCalculationResult result = feeCalculationService.calculateFees(request);

        // Then
        assertThat(result.netBookingPrice()).isEqualTo(new BigDecimal("100.00"));
        assertThat(result.totalAppFee()).isEqualTo(new BigDecimal("7.00"));
        assertThat(result.passPspFeesToCustomer()).isTrue();
        assertThat(result.totalTransactionAmount()).isGreaterThan(netBookingPrice);
    }

    @Test
    void shouldApplyMinimumAppFee() {
        // Given
        BigDecimal netBookingPrice = new BigDecimal("10.00"); // 5% + $2 = $2.50, but min is $3
        FeeCalculationRequest request = FeeCalculationRequest.builder()
                .venueId(VENUE_ID)
                .netBookingPrice(netBookingPrice)
                .build();

        when(feeConfigRepository.findByVenueId(VENUE_ID)).thenReturn(Optional.of(feeConfig));

        // When
        FeeCalculationResult result = feeCalculationService.calculateFees(request);

        // Then
        assertThat(result.totalAppFee()).isEqualTo(new BigDecimal("3.00")); // Minimum applied
    }

    @Test
    void shouldApplyMaximumAppFee() {
        // Given
        BigDecimal netBookingPrice = new BigDecimal("2000.00"); // 5% + $2 = $102, but max is $50
        FeeCalculationRequest request = FeeCalculationRequest.builder()
                .venueId(VENUE_ID)
                .netBookingPrice(netBookingPrice)
                .build();

        when(feeConfigRepository.findByVenueId(VENUE_ID)).thenReturn(Optional.of(feeConfig));

        // When
        FeeCalculationResult result = feeCalculationService.calculateFees(request);

        // Then
        assertThat(result.totalAppFee()).isEqualTo(new BigDecimal("50.00")); // Maximum applied
    }

    @Test
    void shouldThrowException_WhenFeeConfigNotFound() {
        // Given
        FeeCalculationRequest request = FeeCalculationRequest.builder()
                .venueId("non-existent-venue")
                .netBookingPrice(new BigDecimal("100.00"))
                .build();

        when(feeConfigRepository.findByVenueId("non-existent-venue")).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> feeCalculationService.calculateFees(request))
                .isInstanceOf(GeneralException.class)
                .hasMessageContaining("Fee configuration not found for venue: non-existent-venue");
    }

    @Test
    void shouldThrowException_WhenNetBookingPriceIsZeroOrNegative() {
        // Given
        FeeCalculationRequest request = FeeCalculationRequest.builder()
                .venueId(VENUE_ID)
                .netBookingPrice(BigDecimal.ZERO)
                .build();

        when(feeConfigRepository.findByVenueId(VENUE_ID)).thenReturn(Optional.of(feeConfig));

        // When & Then
        assertThatThrownBy(() -> feeCalculationService.calculateFees(request))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("Net booking price must be positive");
    }
}
