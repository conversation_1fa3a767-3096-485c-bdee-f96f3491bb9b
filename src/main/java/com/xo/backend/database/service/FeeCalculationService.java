package com.xo.backend.database.service;

import com.xo.backend.database.entity.fees.FeeConfigEntity;
import com.xo.backend.database.repository.fees.FeeConfigRepository;
import com.xo.backend.error.exceptions.GeneralException;
import com.xo.backend.model.dto.fees.FeeCalculationRequest;
import com.xo.backend.model.dto.fees.FeeCalculationResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Slf4j
@Service
@RequiredArgsConstructor
public class FeeCalculationService {

    private final FeeConfigRepository feeConfigRepository;

    /**
     * Calculates fees and total transaction amount for a booking.
     * The goal is for merchants to receive as close to net_booking_price as possible.
     * 
     * @param request The fee calculation request containing venue ID and net booking price
     * @return FeeCalculationResult with all calculated amounts
     */
    public FeeCalculationResult calculateFees(FeeCalculationRequest request) {
        log.debug("Calculating fees for venue: {} with net booking price: {}", 
                 request.venueId(), request.netBookingPrice());

        FeeConfigEntity feeConfig = feeConfigRepository.findByVenueId(request.venueId())
                .orElseThrow(() -> new GeneralException("Fee configuration not found for venue: " + request.venueId()));

        BigDecimal netBookingPrice = request.netBookingPrice();
        
        // Validate input
        if (netBookingPrice == null || netBookingPrice.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Net booking price must be positive");
        }

        // Calculate app fee
        BigDecimal totalAppFee = calculateAppFee(netBookingPrice, feeConfig);

        // Calculate transaction amount and PSP fees iteratively
        FeeCalculationResult result = calculateTransactionAmountWithPspFees(
                netBookingPrice, totalAppFee, feeConfig);

        log.debug("Fee calculation completed. Transaction amount: {}, Merchant receives: {}", 
                 result.totalTransactionAmount(), result.merchantReceives());

        return result;
    }

    /**
     * Calculates the app fee based on percentage + fixed amount with min/max constraints
     */
    private BigDecimal calculateAppFee(BigDecimal netBookingPrice, FeeConfigEntity feeConfig) {
        // Calculate percentage fee
        BigDecimal percentageFee = netBookingPrice
                .multiply(feeConfig.getAppFeePerc())
                .setScale(2, RoundingMode.HALF_UP);

        // Add fixed fee
        BigDecimal totalAppFee = percentageFee.add(feeConfig.getAppFeeFixed());

        // Apply min constraint
        if (feeConfig.getAppFeeMin() != null && totalAppFee.compareTo(feeConfig.getAppFeeMin()) < 0) {
            totalAppFee = feeConfig.getAppFeeMin();
        }

        // Apply max constraint
        if (feeConfig.getAppFeeMax() != null && totalAppFee.compareTo(feeConfig.getAppFeeMax()) > 0) {
            totalAppFee = feeConfig.getAppFeeMax();
        }

        return totalAppFee.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * Calculates the total transaction amount considering PSP fees.
     * Uses iterative approach to converge on the correct amount since PSP fees are applied to the total.
     */
    private FeeCalculationResult calculateTransactionAmountWithPspFees(
            BigDecimal netBookingPrice, BigDecimal totalAppFee, FeeConfigEntity feeConfig) {

        BigDecimal currentTransactionAmount = netBookingPrice.add(totalAppFee);
        BigDecimal previousTransactionAmount;
        int iterations = 0;
        final int maxIterations = 10; // Prevent infinite loops
        final BigDecimal tolerance = new BigDecimal("0.01"); // 1 cent tolerance

        do {
            previousTransactionAmount = currentTransactionAmount;
            
            // Calculate PSP fee on current transaction amount
            BigDecimal estimatedPspFee = calculatePspFee(currentTransactionAmount, feeConfig);
            
            if (feeConfig.getPassPspFees()) {
                // Customer pays PSP fees - add them to transaction amount
                currentTransactionAmount = netBookingPrice.add(totalAppFee).add(estimatedPspFee);
            } else {
                // Platform absorbs PSP fees - adjust transaction amount so merchant gets net_booking_price
                currentTransactionAmount = netBookingPrice.add(totalAppFee).add(estimatedPspFee);
            }
            
            iterations++;
        } while (iterations < maxIterations && 
                 currentTransactionAmount.subtract(previousTransactionAmount).abs().compareTo(tolerance) > 0);

        // Final PSP fee calculation
        BigDecimal finalEstimatedPspFee = calculatePspFee(currentTransactionAmount, feeConfig);
        
        // Calculate what merchant actually receives
        BigDecimal merchantReceives = currentTransactionAmount.subtract(totalAppFee).subtract(finalEstimatedPspFee);

        // Apply total min/max constraints
        currentTransactionAmount = applyTotalConstraints(currentTransactionAmount, feeConfig);

        return FeeCalculationResult.builder()
                .netBookingPrice(netBookingPrice)
                .totalAppFee(totalAppFee)
                .totalEstimatedPspFee(finalEstimatedPspFee)
                .totalTransactionAmount(currentTransactionAmount)
                .merchantReceives(merchantReceives)
                .passPspFeesToCustomer(feeConfig.getPassPspFees())
                .build();
    }

    /**
     * Calculates PSP fee based on percentage + fixed amount
     */
    private BigDecimal calculatePspFee(BigDecimal transactionAmount, FeeConfigEntity feeConfig) {
        BigDecimal percentageFee = transactionAmount
                .multiply(feeConfig.getPspFeePerc())
                .setScale(2, RoundingMode.HALF_UP);

        return percentageFee.add(feeConfig.getPspFeeFixed())
                .setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * Applies total min/max constraints to the transaction amount
     */
    private BigDecimal applyTotalConstraints(BigDecimal transactionAmount, FeeConfigEntity feeConfig) {
        BigDecimal constrainedAmount = transactionAmount;

        if (feeConfig.getTotalMin() != null && constrainedAmount.compareTo(feeConfig.getTotalMin()) < 0) {
            constrainedAmount = feeConfig.getTotalMin();
        }

        if (feeConfig.getTotalMax() != null && constrainedAmount.compareTo(feeConfig.getTotalMax()) > 0) {
            constrainedAmount = feeConfig.getTotalMax();
        }

        return constrainedAmount;
    }

    /**
     * Gets fee configuration for a venue
     */
    public FeeConfigEntity getFeeConfigByVenueId(String venueId) {
        return feeConfigRepository.findByVenueId(venueId)
                .orElseThrow(() -> new GeneralException("Fee configuration not found for venue: " + venueId));
    }
}
