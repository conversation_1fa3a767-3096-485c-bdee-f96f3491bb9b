package com.xo.backend.database.repository.fees;

import com.xo.backend.database.entity.fees.FeeConfigEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface FeeConfigRepository extends JpaRepository<FeeConfigEntity, Integer> {
    
    Optional<FeeConfigEntity> findByVenueId(String venueId);
    
    boolean existsByVenueId(String venueId);
}
