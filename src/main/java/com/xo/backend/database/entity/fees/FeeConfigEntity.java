package com.xo.backend.database.entity.fees;

import com.xo.backend.database.entity.audit.DateAudit;
import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;

@Entity
@Table(name = "fee_config", schema = "xo")
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
public class FeeConfigEntity extends DateAudit {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "venue_id", nullable = false, unique = true)
    private String venueId;

    @Column(name = "app_fee_perc", nullable = false, precision = 5, scale = 4)
    private BigDecimal appFeePerc;

    @Column(name = "app_fee_fixed", nullable = false, precision = 19, scale = 2)
    private BigDecimal appFeeFixed;

    @Column(name = "app_fee_min", precision = 19, scale = 2)
    private BigDecimal appFeeMin;

    @Column(name = "app_fee_max", precision = 19, scale = 2)
    private BigDecimal appFeeMax;

    @Column(name = "psp_fee_perc", nullable = false, precision = 5, scale = 4)
    private BigDecimal pspFeePerc;

    @Column(name = "psp_fee_fixed", nullable = false, precision = 19, scale = 2)
    private BigDecimal pspFeeFixed;

    @Column(name = "total_min", precision = 19, scale = 2)
    private BigDecimal totalMin;

    @Column(name = "total_max", precision = 19, scale = 2)
    private BigDecimal totalMax;

    @Column(name = "pass_psp_fees", nullable = false)
    @Builder.Default
    private Boolean passPspFees = false;
}
