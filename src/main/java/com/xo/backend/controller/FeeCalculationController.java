package com.xo.backend.controller;

import com.xo.backend.database.service.FeeCalculationService;
import com.xo.backend.model.dto.fees.FeeCalculationRequest;
import com.xo.backend.model.dto.fees.FeeCalculationResult;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/v1/fees")
@RequiredArgsConstructor
public class FeeCalculationController {

    private final FeeCalculationService feeCalculationService;

    @PostMapping("/calculate")
    @PreAuthorize("hasRole('ADMIN') OR hasRole('PLATFORM_ADMIN')")
    public ResponseEntity<FeeCalculationResult> calculateFees(@Valid @RequestBody FeeCalculationRequest request) {
        FeeCalculationResult result = feeCalculationService.calculateFees(request);
        return ResponseEntity.ok(result);
    }
}
