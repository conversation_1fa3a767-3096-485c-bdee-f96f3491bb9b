package com.xo.backend.model.dto.fees;

import lombok.Builder;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

import java.math.BigDecimal;

@Builder
public record FeeCalculationRequest(
        @NotBlank(message = "Venue ID is required")
        String venueId,

        @NotNull(message = "Net booking price is required")
        @Positive(message = "Net booking price must be positive")
        BigDecimal netBookingPrice
) {
}
