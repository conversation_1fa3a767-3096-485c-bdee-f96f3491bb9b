package com.xo.backend.service;

import com.xo.backend.client.email.EmailService;
import com.xo.backend.client.user.UserInfoService;
import com.xo.backend.client.venues.VenueInfoService;
import com.xo.backend.database.entity.bookings.*;
import com.xo.backend.database.entity.events.EventEntity;
import com.xo.backend.database.repository.booking.BookingRepository;
import com.xo.backend.database.repository.entry_pass.EntryPassCount;
import com.xo.backend.database.repository.entry_pass.EntryPassRepository;
import com.xo.backend.database.repository.event.EventRepository;
import com.xo.backend.error.exceptions.BookingNotFoundException;
import com.xo.backend.error.exceptions.EventNotFoundException;
import com.xo.backend.error.exceptions.InsufficientPermissionsException;
import com.xo.backend.error.exceptions.InvalidBookingStatusException;
import com.xo.backend.mappers.AdminDetailedBookingMapper;
import com.xo.backend.mappers.BookingMapper;
import com.xo.backend.mappers.message.BookingMessageMapper;
import com.xo.backend.model.dto.*;
import com.xo.backend.model.dto.go.GoUserProfileResponseDTO;
import com.xo.backend.model.dto.go.VenueDTO;
import com.xo.backend.model.dto.request.booking.BookingStatusBulkUpdateRequestDTO;
import com.xo.backend.model.dto.request.booking.BookingStatusUpdateRequestDTO;
import com.xo.backend.model.dto.responses.CreateBookingResponseDTO;
import com.xo.backend.specs.BookingSpecs;
import com.xo.backend.utlis.BookingHelper;
import com.xo.backend.utlis.BookingReferenceGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.statemachine.config.StateMachineFactory;
import org.springframework.statemachine.recipes.persist.FactoryPersistStateMachineHandler;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.*;

import static com.xo.backend.database.entity.bookings.BookingStatus.CONFIRMED;
import static com.xo.backend.database.entity.bookings.BookingStatus.DRAFT;
import static com.xo.backend.service.BookingStateMachineConfig.BOOKING_ID;
import static com.xo.backend.service.BookingStateMachineConfig.BOOKING_MESSAGE;

@Service
@RequiredArgsConstructor
@Slf4j
public class AdminBookingService {

    public static final String EVENT_NAME = "eventName";
    public static final String OWNER_NAME = "ownerName";
    public static final String EVENT_ID = "eventId";
    public static final String BOOKING_STATUS = "bookingStatus";
    public static final String BOOKING_NUMBER = "bookingNumber";
    public static final String BOOKING_CHANNEL = "bookingChannel";
    public static final String OWNER_ID = "ownerId";

    private final BookingRepository bookingRepository;
    private final BookingMapper bookingMapper;
    private final EventRepository eventRepository;
    private final AdminDetailedBookingMapper adminDetailedBookingMapper;
    private final BookingHelper bookingHelper;
    private final BookingMessageMapper bookingMessageMapper;
    private final EntryPassService entryPassService;
    private final EntryPassRepository entryPassRepository;
    private final FactoryPersistStateMachineHandler<BookingStatus, BookingStatus> bookingStateMachineHandler;
    private final StateMachineFactory<BookingStatus, BookingStatus> bookingStateMachineFactory;
    private final EmailService emailService;
    private final UserInfoService userInfoService;
    private final VenueInfoService venueInfoService;

    @Transactional
    public CreateBookingResponseDTO createAdminBooking(String venueId, Integer eventId, AdminCreateBookingRequest adminCreateBookingRequest) {

        EventEntity eventEntity = eventRepository.findById(eventId).orElseThrow(EventNotFoundException::new);

        if (!eventEntity.getVenueId().equals(venueId)) {
            throw new InsufficientPermissionsException();
        }

        GoUserProfileResponseDTO goUserProfileResponseDTO = userInfoService.getUserProfile();

        BookingEntity.BookingEntityBuilder bookingEntityBuilder = BookingEntity.builder()
                .ownerId(adminCreateBookingRequest.ownerId())
                .eventId(eventId)
                .ownerName(adminCreateBookingRequest.ownerName())
                .ownerEmail(adminCreateBookingRequest.ownerEmail())
                .ownerPhone(adminCreateBookingRequest.ownerPhone())
                .guestList(adminCreateBookingRequest.guestList())
                .event(eventEntity)
                .paymentStatus(adminCreateBookingRequest.paymentStatus())
                .status(adminCreateBookingRequest.status())
                .bookingNumber(BookingReferenceGenerator.generateReferenceNumber())
                .bookingName(adminCreateBookingRequest.ownerName() + " Booking")
                .bookingOccasion(adminCreateBookingRequest.bookingOccasion())
                .comment(adminCreateBookingRequest.comment())
                .bookingChannel(BookingChannel.ADMIN_DASHBOARD)
                .notes(adminCreateBookingRequest.notes())
                .bookingItems(new ArrayList<>())
                .creatorName(goUserProfileResponseDTO.firstName() + " " + goUserProfileResponseDTO.lastName());

        if (DRAFT != adminCreateBookingRequest.status()) bookingEntityBuilder.submittedAt(Instant.now());

        //check booking does not contain mixed items
        BookingType bookingType = bookingHelper.getBookingType(adminCreateBookingRequest.bookingItems());
        bookingHelper.validateBooking(bookingType, adminCreateBookingRequest.bookingItems());
        BookingEntity bookingEntity = bookingEntityBuilder.build();
        bookingEntity = bookingRepository.save(bookingEntity);

        // create booking items
        List<BookingItemEntity> bookingItemEntityList = bookingHelper.prepareBookingItems(adminCreateBookingRequest.bookingItems(),
                bookingEntity.getId(), eventEntity.getId(), false);

        bookingEntity.getBookingItems().addAll(bookingItemEntityList);

        bookingEntity.setBookingType(bookingType);

        bookingHelper.calculateBookingPaymentAmount(bookingEntity, bookingItemEntityList);

        bookingEntity.setRequireConfirmation(bookingHelper.isBookingConfirmationRequired(bookingEntity));

        bookingEntity = bookingRepository.save(bookingEntity);

        if (bookingEntity.getStatus() == CONFIRMED) {
            /*Update availability and create entry passes*/
            entryPassService.createEntryPassesAndUpdateAvailability(bookingEntity);
        }
        /* Enable this functionality once Admin UI renders button to get payment session */
//        else if (bookingEntity.getStatus() == APPROVED && bookingEntity.getTotalAmount().compareTo(BigDecimal.ZERO) > 0 && bookingEntity.getPaymentStatus() != PaymentStatus.PAID) {
//            paymentService.createCheckoutSession(bookingEntity.getId(), PaymentProvider.STRIPE);
//        }

        return CreateBookingResponseDTO.builder()
                .booking(bookingMapper.mapToBookingDTO(bookingEntity))
                .build();
    }

    @Transactional
    public BookingDTO updateBookingStatus(String venueId, Integer bookingId, BookingStatusUpdateRequestDTO bookingStatusUpdateRequestDTO) {

        BookingEntity booking = bookingRepository.findById(bookingId).orElseThrow(BookingNotFoundException::new);

        /*Same status hence skip*/
        if(booking.getStatus()==bookingStatusUpdateRequestDTO.status()){
            return bookingMapper.mapToBookingDTO(booking);
        }

        if (!booking.getEvent().getVenueId().equals(venueId)) {
            throw new InsufficientPermissionsException();
        }

        updateBookingStatus(booking, bookingStatusUpdateRequestDTO.status());

        log.info("Booking state after handler call {}", booking.getStatus());
        return bookingMapper.mapToBookingDTO(booking);

    }


    @Transactional
    public List<BookingDTO> bulkUpdateBookingStatus(String venueId, BookingStatusBulkUpdateRequestDTO bookingStatusUpdateRequest) {

        List<BookingEntity> bookings = bookingRepository.findAllById(bookingStatusUpdateRequest.bookingIds());

        if (!bookings.stream().allMatch(booking -> booking.getEvent().getVenueId().equals(venueId))) {
            throw new InsufficientPermissionsException();
        }

        List<BookingEntity> updatedBookings = new ArrayList<>();
        for (BookingEntity booking : bookings) {
            /*Same status hence skip*/
            if(booking.getStatus()==bookingStatusUpdateRequest.status()){
                continue;
            }
            updateBookingStatus(booking, bookingStatusUpdateRequest.status());
            updatedBookings.add(booking);
        }

        emailService.sendBulkEmails(updatedBookings);

        return updatedBookings.stream().map(bookingMapper::mapToBookingDTO).toList();
    }

    private void updateBookingStatus(BookingEntity booking, BookingStatus bookingStatusUpdateRequest) {
        BookingStatus oldBookingStatus = booking.getStatus();
        bookingStateMachineHandler.
                handleEventWithStateReactively(MessageBuilder.withPayload(bookingStatusUpdateRequest)
                                .setHeader(BOOKING_MESSAGE,bookingMessageMapper.getBookingMessage(booking))
                                .build(),
                        booking.getStatus())
                .block();
        if (booking.getStatus() == oldBookingStatus) {
            throw new InvalidBookingStatusException("Invalid booking status action : " + bookingStatusUpdateRequest);
        }
    }

    @Transactional(readOnly = true)
    public Page<AdminDetailedBookingDTO> getBookingsByFilter(String venueId, Map<String, String> queryParams, Pageable pageable) {
        // remove this once clients migrate

        // Build Specification dynamically
        Specification<BookingEntity> bookingEntitySpecification = queryParams.entrySet().stream()
                .map(entry -> buildSpecification(entry.getKey(), entry.getValue()))
                .filter(Objects::nonNull) // Ignore null specifications
                .reduce(Specification::and) // Combine all specifications with AND
                .orElse((root, query, cb) -> cb.and())
                .and(BookingSpecs.withVenueId(venueId)) // Filter by venue
                .and(BookingSpecs.excludeDraftStatus()); // Exclude DRAFT bookings

        Page<BookingEntity> bookingEntities = bookingRepository.findAll(bookingEntitySpecification, pageable);
        VenueDTO venueDTO=venueInfoService.getVenueDetails(venueId);
        return bookingEntities.map(bookingEntity -> {
            EntryPassCount entryPassCount = entryPassRepository.getEntryPassCountsByBookingId(bookingEntity.getId());
            return adminDetailedBookingMapper.mapToDetailedBookingDTO(bookingEntity, entryPassCount, bookingStateMachineFactory.getStateMachine(),venueDTO);
        });
    }

    private Specification<BookingEntity> buildSpecification(String key, String value) {
        return switch (key) {
            case EVENT_NAME -> BookingSpecs.withEventName(value);
            case BOOKING_ID -> BookingSpecs.withBookingId(Integer.valueOf(value));
            case OWNER_NAME -> BookingSpecs.withOwnerName(value);
            case EVENT_ID -> BookingSpecs.withEventId(Integer.valueOf(value));
            case BOOKING_STATUS -> BookingSpecs.withBookingStatus(BookingStatus.valueOf(value));
            case BOOKING_NUMBER -> BookingSpecs.withBookingNumber(value);
            case BOOKING_CHANNEL -> BookingSpecs.withBookingChannel(BookingChannel.valueOf(value));
            case OWNER_ID -> BookingSpecs.withOwnerId(Integer.valueOf(value));
            default -> null; // Ignore unknown parameters
        };
    }

    @Transactional
    public BookingDTO updateBookingPartially(String venueId, Integer bookingId, AdminUpdateBookingRequest adminUpdateBookingRequest) {
        BookingEntity booking = bookingRepository.findById(bookingId).orElseThrow(BookingNotFoundException::new);
        if (!booking.getEvent().getVenueId().equals(venueId)) {
            throw new InsufficientPermissionsException();
        }
        Optional.ofNullable(adminUpdateBookingRequest.notes()).ifPresent(booking::setNotes);
        Optional.ofNullable(adminUpdateBookingRequest.paymentStatus()).ifPresent(booking::setPaymentStatus);
        return bookingMapper.mapToBookingDTO(booking);
    }

    @Transactional
    public BookingStatisticsDTO getBookingStatistics(String venueId, Integer userId) {
        BookingStatistics bookingStatistics = bookingRepository.findBookingStatistics(venueId, userId);
        return BookingStatisticsDTO.builder()
                .bookingCount(bookingStatistics.getBookingCount())
                .timeZone( venueInfoService.getVenueDetails(venueId).timeZone())
                .firstBookingDate(bookingStatistics.getFirstBookingDate())
                .lastBookingDate(bookingStatistics.getLastBookingDate())
                .reservationCount(bookingStatistics.getReservationCount())
                .ticketCount(bookingStatistics.getTicketCount())
                .totalSpent(bookingStatistics.getTotalSpent())
                .build();
    }
}
